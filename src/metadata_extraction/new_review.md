
---

### On `docmeta/core/factory.py`

```markdown
Enzo DAMION commented X days ago
```

> The function signature is `-> MetadataType`, but `merged_metadata` is `dict[str, Any]`. While it's constructed from `FileMetaData` and the `specific_metadata_dict`, static type checkers won't automatically infer that this `dict` conforms to one of the complex `TypedDict`s in `MetadataType`. This will likely cause type errors or require a `# type: ignore` on the return statement.

**Explanation:**
The `extract_metadata` function is declared to return `MetadataType` (a union of specific TypedDicts like `PDFMetaData`, `ImageMetaData`, etc.). However, the actual `merged_metadata` object is a plain `dict[str, Any]`. Python's type system won't automatically know that this dictionary's structure matches one of the `MetadataType` variants.

**Problematic Code:**
```python
# docmeta/core/factory.py
from docmeta.core.types import MetadataType, FileMetaData # ... other imports

def extract_metadata(path: str, count_tokens_flag: bool = False, extract_keywords_language: str | None = None) -> MetadataType:
    # ...
    common_metadata: FileMetaData = get_file_common_metadata(path)
    specific_metadata_dict: dict[str, Any] = {}
    # ...
    if extractor_func:
        specific_metadata_dict, text_content = extractor_func(path)

    merged_metadata: dict[str, Any] = {**specific_metadata_dict, **common_metadata}
    # ...
    return merged_metadata # merged_metadata is dict[str, Any], but return type is MetadataType
```

**Suggested Fix (Illustrative - acknowledges the issue):**
```python
# docmeta/core/factory.py
from typing import cast # Add cast
from docmeta.core.types import (
    MetadataType, FileMetaData, PDFMetaData, ImageMetaData # etc.
)

def extract_metadata(path: str, count_tokens_flag: bool = False, extract_keywords_language: str | None = None) -> MetadataType:
    validate_file_exists(path)
    common_metadata: FileMetaData = get_file_common_metadata(path)
    specific_metadata_dict: dict[str, Any] = {}
    text_content: str | None = None
    # Determine the expected specific type (this logic is simplified)
    # In a real scenario, you'd map extensions/MIME types to their TypedDicts
    expected_type = FileMetaData # Default or determine dynamically

    extractor_func = get_extractor(path)

    if extractor_func:
        # This is complex: how do you know which TypedDict variant specific_metadata_dict aligns with?
        # For simplicity, we'll assume the factory or extractor registration could provide this info.
        # Let's say for a PDF, we know it should be PDFMetaData
        # This part needs more robust logic based on the extractor chosen.
        if common_metadata["file_extension"] == ".pdf":
            expected_type = PDFMetaData
        elif common_metadata["file_extension"] in [".jpg", ".png"]:
            expected_type = ImageMetaData
        # ... and so on for other types

        specific_metadata_dict, text_content = extractor_func(path)

    merged_metadata_dict: dict[str, Any] = {**specific_metadata_dict, **common_metadata}

    if text_content and text_content.strip():
        if count_tokens_flag:
            merged_metadata_dict["token_count"] = count_tokens(text_content)
        if extract_keywords_language:
            extracted_keywords = extract_keywords_tfidf(text_content, language=extract_keywords_language)
            if extracted_keywords:
                merged_metadata_dict["extracted_keywords"] = extracted_keywords
    
    # This cast is an assertion to the type checker.
    # It's the developer's responsibility to ensure merged_metadata_dict
    # actually conforms to the expected_type structure.
    return cast(expected_type, merged_metadata_dict)
    # Alternatively, to just suppress the error if confident:
    # return merged_metadata_dict # type: ignore
```
*This is a deeper architectural issue. The most robust solution is for individual extractors to return their specific `TypedDict` (e.g., `PDFMetaData`), and the factory would then merge that with `FileMetaData` while preserving the specific type.*

